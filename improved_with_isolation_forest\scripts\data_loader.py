import numpy as np
import logging
import os

def load_data_for_group(group: str, train_path: str = None, test_path: str = None):
    """
    根据指定的组别，从.npz文件中加载并划分数据。
    严格遵循论文 "A Novel Zero-Shot Learning Method With Feature Generation for Intelligent Fault Diagnosis"
    中的 Table IV 定义。

    Args:
        group (str): 要加载的数据组别 ('A', 'B', 'C', 'D', 'E').
        train_path (str): 训练数据 .npz 文件路径.
        test_path (str): 测试数据 .npz 文件路径.

    Returns:
        Tuple: (traindata, train_attributelabel, testdata, test_attributelabel)
    """
    logger = logging.getLogger('BaselineTrainer.DataLoader')

    # 自动检测数据文件路径
    if train_path is None or test_path is None:
        # 尝试从当前目录开始查找
        possible_paths = [
            'data/',  # 当前目录下的data文件夹
            '../data/',  # 上级目录的data文件夹
            '../../data/',  # 上上级目录的data文件夹
        ]

        data_dir = None
        for path in possible_paths:
            if os.path.exists(os.path.join(path, 'dataset_train_case1.npz')):
                data_dir = path
                break

        if data_dir is None:
            raise FileNotFoundError("无法找到数据文件！请确保dataset_train_case1.npz和dataset_test_case1.npz存在于data目录中")

        train_path = os.path.join(data_dir, 'dataset_train_case1.npz')
        test_path = os.path.join(data_dir, 'dataset_test_case1.npz')

    logger.info(f"使用数据路径: train={train_path}, test={test_path}")

    # 论文 Table IV 定义
    group_definitions = {
        'A': {'unseen': [1, 6, 14]},
        'B': {'unseen': [4, 7, 10]},
        'C': {'unseen': [8, 11, 12]},
        'D': {'unseen': [2, 3, 5]},
        'E': {'unseen': [9, 13, 15]}
    }
    
    if group not in group_definitions:
        raise ValueError(f"无效的组别: {group}. 必须是 'A', 'B', 'C', 'D', 'E' 中的一个。")

    logger.info(f"为组别 {group} 定义数据，看不见的类别: {group_definitions[group]['unseen']}")

    try:
        train_data_npz = np.load(train_path)
        test_data_npz = np.load(test_path)
    except FileNotFoundError:
        logger.error(f"数据文件未找到! 请确保 '{train_path}' 和 '{test_path}' 存在。")
        raise

    all_classes = list(range(1, 16))
    unseen_classes = group_definitions[group]['unseen']
    seen_classes = [c for c in all_classes if c not in unseen_classes]
    
    logger.info(f"看得见的类别: {seen_classes}")

    # 加载所有训练样本和属性
    train_samples = [train_data_npz[f'training_samples_{i}'] for i in seen_classes]
    train_attributes = [train_data_npz[f'training_attribute_{i}'] for i in seen_classes]
    
    # 加载所有测试样本和属性
    test_samples = [test_data_npz[f'testing_samples_{i}'] for i in unseen_classes]
    test_attributes = [test_data_npz[f'testing_attribute_{i}'] for i in unseen_classes]

    # 拼接数据
    traindata = np.concatenate(train_samples, axis=0)
    train_attributelabel = np.concatenate(train_attributes, axis=0)
    testdata = np.concatenate(test_samples, axis=0)
    test_attributelabel = np.concatenate(test_attributes, axis=0)
    
    logger.info(f"数据加载完成:")
    logger.info(f"  - 训练数据形状: {traindata.shape}")
    logger.info(f"  - 训练属性形状: {train_attributelabel.shape}")
    logger.info(f"  - 测试数据形状: {testdata.shape}")
    logger.info(f"  - 测试属性形状: {test_attributelabel.shape}")
    
    return traindata, train_attributelabel, testdata, test_attributelabel, unseen_classes

if __name__ == '__main__':
    # 用于测试数据加载器
    logging.basicConfig(level=logging.INFO)
    print("测试组别 A 的数据加载...")
    load_data_for_group('A')
    print("\n测试组别 B 的数据加载...")
    load_data_for_group('B')
