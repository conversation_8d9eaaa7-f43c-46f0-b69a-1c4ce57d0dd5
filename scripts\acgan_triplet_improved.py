import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU, LayerNormalization, BatchNormalization, Conv1D, Reshape, concatenate, Flatten, Dropout, Concatenate, multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import logging
import sys
from tensorflow.keras.losses import mean_squared_error
from sklearn.preprocessing import MinMaxScaler
from scipy.spatial.distance import pdist, squareform # 需要在文件顶部导入
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier

# 【归一化层选择】使用LayerNormalization，因为它在GAN训练中表现更好
# 虽然可能会有GPU警告信息，但实际上是可以正常工作的
NormalizationLayer = LayerNormalization
print("✅ 使用 LayerNormalization（推荐用于GAN训练）")

def feature_generation_and_diagnosis(num_samples, test_data, test_attribute, autoencoder, generator, classifier, test_class_indices):
    """
    生成指定数量的特征样本，并使用多种分类器进行诊断评估。

    参数:
    - num_samples (int): 需要为每个未见过的类别生成的样本数量。
    - test_data (np.array): 包含所有测试类别样本的数组。
    - test_attribute (np.array): 包含所有测试类别属性的数组。
    - autoencoder (Model): 训练好的自编码器模型，用于提取真实样本的特征。
    - generator (Model): 训练好的生成器模型。
    - classifier (Model): 训练好的分类器模型（这里未使用，但保留接口一致性）。
    - test_class_indices (list): 未见过的类别的索引列表。

    返回:
    - tuple: 包含四种分类器准确率的元组 (LSVM, RF, PNB, MLP)。
    """
    # 1. 提取真实测试样本的特征
    real_features, _ = autoencoder.predict(test_data)
    
    # 2. 为每个未见过的类别生成伪特征
    generated_features_list = []
    generated_labels_list = []
    
    # 获取每个测试类别的唯一属性
    unique_attributes = {}
    current_pos = 0
    # 假设 test_attribute 是按类别顺序排列的
    # 我们需要找到每个类别的第一个样本的属性作为代表
    # 注意：这部分逻辑依赖于 test_attribute 的组织方式
    # 一个更稳健的方法是预先知道每个类别有多少样本
    # 这里我们简化处理，假设可以从 test_attribute 中提取出唯一属性
    
    # 提取唯一属性和对应标签
    unique_attrs, unique_indices = np.unique(test_attribute, axis=0, return_index=True)
    # unique_labels = [test_class_indices[i] for i in np.argsort(unique_indices)] # 这行可能不准，直接用 test_class_indices
    
    if len(unique_attrs) != len(test_class_indices):
        print(f"Warning: Found {len(unique_attrs)} unique attributes, but expected {len(test_class_indices)}.")
        # Fallback or error handling
        # For now, we'll proceed but this might indicate a data issue.

    for i, class_index in enumerate(test_class_indices):
        # 使用唯一的属性向量来生成
        class_attribute = unique_attrs[i]
        
        # 构造生成器输入
        noise = tf.random.normal(shape=(num_samples, generator.input_shape[0][1], 1))
        # 将单个属性向量复制 num_samples 次
        attribute_input = np.tile(class_attribute, (num_samples, 1))
        
        # 生成特征
        gen_features = generator.predict([noise, attribute_input])
        
        # 【修复点3】检查生成的特征是否包含NaN
        if np.isnan(gen_features).any():
            print(f"[ERROR] Generated features contain NaN for class {class_index}. Generator training may be unstable.")
            print("Returning zero accuracies to avoid sklearn errors.")
            return 0.0, 0.0, 0.0, 0.0
        
        generated_features_list.append(gen_features)
        
        # 创建对应的标签
        generated_labels_list.append(np.full(num_samples, class_index))

    generated_features = np.concatenate(generated_features_list, axis=0)
    generated_labels = np.concatenate(generated_labels_list, axis=0)

    # 3. 准备训练集和测试集
    # 训练集 = 生成的伪特征
    X_train = generated_features
    y_train = generated_labels
    
    # 测试集 = 真实的测试特征
    # 我们需要为真实的测试特征创建标签
    # 这部分逻辑也需要知道 test_data 中每个类别的样本数
    # 假设 test_data 和 test_attribute 是按类别顺序排列的
    real_labels = []
    samples_per_class = len(test_data) // len(test_class_indices)
    if len(test_data) % len(test_class_indices) != 0:
        print("Warning: Test data may not be evenly distributed among classes.")

    for class_index in test_class_indices:
        real_labels.extend([class_index] * samples_per_class)
    
    X_test = real_features
    y_test = np.array(real_labels)

    # 4. 使用多种分类器进行训练和评估
    classifiers = {
        "LSVM": SVC(kernel='linear', probability=True),
        "RF": RandomForestClassifier(n_estimators=100),
        "PNB": GaussianNB(),
        "MLP": MLPClassifier(hidden_layer_sizes=(100,), max_iter=300)
    }
    
    accuracies = {}
    
    for name, clf in classifiers.items():
        clf.fit(X_train, y_train)
        accuracy = clf.score(X_test, y_test)
        accuracies[name] = accuracy
        # print(f"{name} Accuracy: {accuracy:.4f}")

    return accuracies.get("LSVM", 0), accuracies.get("RF", 0), accuracies.get("PNB", 0), accuracies.get("MLP", 0)

# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    # 限制TensorFlow只使用第一个GPU
    tf.config.set_visible_devices(gpus[0], 'GPU')
    # 设置显存按需增长
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")

    # 【修复LayerNormalization GPU兼容性问题】
    # 禁用MKL优化以避免LayerNormalization在GPU上的问题
    tf.config.optimizer.set_experimental_options({'disable_meta_optimizer': True})

  except RuntimeError as e:
    # 显存增长必须在GPU初始化之前设置
    print(e)


def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x

    y = Dense(units)(x)
    y = NormalizationLayer()(y)
    y = LeakyReLU(alpha=0.2)(y)

    y = Dense(units)(y)
    y = NormalizationLayer()(y)

    # 如果输入和输出维度不同，需要一个线性投影
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)

    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        # input_shape is (batch_size, feature_dim)
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        # Reshape for matrix multiplication
        # Temporarily add a "sequence length" of 1
        # x_reshaped shape: (batch_size, 1, feature_dim)
        x_reshaped = K.expand_dims(x, axis=1)

        # Q, K, V projections
        q = self.query(x_reshaped)  # (batch_size, 1, feature_dim/8)
        k = self.key(x_reshaped)    # (batch_size, 1, feature_dim/8)
        v = self.value(x_reshaped)  # (batch_size, 1, feature_dim)

        # Attention scores
        attention_scores = K.batch_dot(q, k, axes=[2, 2]) # (batch_size, 1, 1)
        attention_probs = K.softmax(attention_scores)

        # Apply attention
        context = K.batch_dot(attention_probs, v) # (batch_size, 1, feature_dim)
        
        # Remove the temporary dimension
        context = K.squeeze(context, axis=1)

        # Add back to original input (residual connection)
        return x + self.gamma * context
        
class Zero_shot():
    def __init__(self, log_dir=None):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)

        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        # 【改进点1】调整损失权重设置，提升训练稳定性
        self.lambda_cla = 0.5  # 进一步降低分类损失权重
        self.lambda_triplet = 0.5  # 进一步降低triplet损失权重
        self.lambda_crl = 0.05  # 降低循环一致性约束权重

        self.bound = True
        self.mi_weight = 0.0005  # 降低互信息权重
        self.mi_bound = 50  # 降低互信息边界
        self.triplet_margin = 0.1  # 减小triplet margin

        # 【改进点2】降低学习率，提升训练稳定性
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005, clipnorm=1.0)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005, clipnorm=1.0)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005, clipnorm=1.0)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005, clipnorm=1.0)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005, clipnorm=1.0) # For triplet loss

        # 日志和TensorBoard设置
        self.log_dir = log_dir
        self.logger = None
        self.summary_writer = None
        self.global_step = 0

        # 用于跟踪历史最佳准确率
        self.best_accs = {'lsvm': 0.0, 'nrf': 0.0, 'pnb': 0.0, 'mlp': 0.0}
        self.overall_best_acc = 0.0

        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()

    def setup_logging(self, group_name):
        """设置日志系统和TensorBoard"""
        if self.log_dir is None:
            current_time = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
            self.log_dir = f"logs/triplet_improved/Group-{group_name}_{current_time}"

        os.makedirs(self.log_dir, exist_ok=True)

        # 设置日志
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.log_dir, 'training.log')),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('TripletImproved')

        # 设置TensorBoard
        self.summary_writer = tf.summary.create_file_writer(self.log_dir)

        self.logger.info(f"日志系统已初始化，日志目录: {self.log_dir}")
        self.logger.info(f"TensorBoard日志目录: {self.log_dir}")

        return self.log_dir
        
    def build_autoencoder(self):
      
      sample = Input(shape=self.sample_shape)     
     
      a0=sample

      # Encoder
      a1=Dense(100)(a0)
      a1=LeakyReLU(alpha=0.2)(a1)
      a1=NormalizationLayer()(a1)

      a2=Dense(200)(a1)
      a2=LeakyReLU(alpha=0.2)(a2)
      a2=NormalizationLayer()(a2)

      a3=Dense(256)(a2)
      a3=LeakyReLU(alpha=0.2)(a3)
      a3=NormalizationLayer()(a3)
      feature=a3

      # Decoder
      a4=Dense(200)(feature)
      a4=LeakyReLU(alpha=0.2)(a4)
      a4=NormalizationLayer()(a4)

      a5=Dense(100)(a4)
      a5=LeakyReLU(alpha=0.2)(a5)
      a5=NormalizationLayer()(a5)

      a6=Dense(52)(a5)
      a6=LeakyReLU(alpha=0.2)(a6)
      a6=NormalizationLayer()(a6)
      output_sample=a6

      # Autoencoder Model
      autoencoder = Model(sample,[feature, output_sample])
      # We only need the encoder part for triplet loss feature extraction
      self.encoder = Model(sample, feature)
      return autoencoder    
        
    def build_discriminator(self):
        
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input,attribute],validity)

    def build_generator(self):
        """【改进点2】引入后期属性调制机制的改进生成器"""
        
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        # --- 早期融合部分保持不变 ---
        noise_embedding = Flatten()(noise)
        attribute_embedding_early = Dense(self.latent_dim)(attribute)
        g_input = concatenate([noise_embedding, attribute_embedding_early])

        # --- 网络主干保持不变，残差块创新得以保留 ---
        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = NormalizationLayer()(g1)

        g2 = residual_block(g1, 256)
        g3 = residual_block(g2, 256)
        intermediate_feature = g3 # 这是经过残差块后的中间特征

        # --- 关键创新点：属性信号后期注入 (Late Fusion) ---
        # 1. 将原始属性向量映射到与中间特征相同的维度，创建一个"引导向量"
        attribute_guide = Dense(256, activation='relu')(attribute)
        attribute_guide = NormalizationLayer()(attribute_guide) # 增加稳定性

        # 2. 使用 multiply 将属性引导向量"调制"到中间特征上
        #    这比简单的拼接更能强化属性的指导作用
        guided_feature = multiply([intermediate_feature, attribute_guide])

        # --- 在被属性引导后的特征上，应用自注意力创新 ---
        #    这样，自注意力机制就能在一个更具语义方向性的特征上工作
        g_attention = SelfAttention()(guided_feature)

        # --- 最终输出层 ---
        generated_feature = Dense(256)(g_attention)
        generated_feature = NormalizationLayer()(generated_feature)

        return Model([noise,attribute],generated_feature)
    
    def build_classifier(self):
        
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])

    def triplet_loss(self, anchor, positive, negative):
        # 【改进点3】添加数值稳定性检查
        anchor = tf.clip_by_norm(anchor, 10.0)  # 限制特征范数
        positive = tf.clip_by_norm(positive, 10.0)
        negative = tf.clip_by_norm(negative, 10.0)

        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)

        # 添加小的epsilon避免数值不稳定
        pos_dist = tf.clip_by_value(pos_dist, 1e-8, 100.0)
        neg_dist = tf.clip_by_value(neg_dist, 1e-8, 100.0)

        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))

        # 确保损失不会过大
        loss = tf.clip_by_value(loss, 0.0, 10.0)
        return loss

    def wasserstein_loss(self, y_true, y_pred):
            return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(
                y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
          mi_penalty = self.mi_penalty_loss(
              current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        # We can reuse the triplet loss logic for cycle rank consistency
        # The goal is that the reconstructed feature (positive) is closer to the original fake feature (anchor)
        # than any other generated feature from a different class (negative).
        return self.triplet_loss(anchor, positive, negative)
    
    def train(self, epochs, batch_size, log_file=None, test_class_indices=None, group_name='C'):

        # 设置日志系统
        log_dir = self.setup_logging(group_name)

        start_time = datetime.datetime.now()
        self.logger.info(f"开始训练 Group {group_name}，共 {epochs} 轮，批次大小: {batch_size}")
        self.logger.info(f"测试类别: {test_class_indices}")

        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]

        valid = -np.ones((batch_size,1) )
        fake = np.ones((batch_size,1) )
        
        PATH_train='/app/data/dataset_train_case1.npz'
        PATH_test='/app/data/dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        # 使用传入的测试类别，如果没有传入则默认使用E组
        if test_class_indices is None:
            test_class_indices = [9, 13, 15]  # 默认E组 (1-based)
        
        # 根据测试类别，确定训练类别（Seen classes）
        all_class_indices = list(range(1, 16)) # 1-15
        seen_class_indices = [i for i in all_class_indices if i not in test_class_indices]
        
        # This part remains mostly the same, but we will use the labels to find pos/neg samples
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_class_indices}
        train_Y_by_class = {i: train_data[f'training_attribute_{i}'] for i in seen_class_indices}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X_by_class = {i: test_data[f'testing_samples_{i}'] for i in test_class_indices}
        test_Y_by_class = {i: test_data[f'testing_attribute_{i}'] for i in test_class_indices}

        test_X = np.concatenate([v for k, v in test_X_by_class.items()])
        test_Y = np.concatenate([v for k, v in test_Y_by_class.items()])
        test_classlabel = np.concatenate([np.full(len(v), k) for k, v in test_X_by_class.items()])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # Re-organize scaled data back into class dictionaries
        current_pos = 0
        for i in seen_class_indices:
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata=all_train_X
        train_attributelabel=all_train_Y
        train_classlabel = all_train_labels
        
        testdata=test_X
        test_attributelabel=test_Y
       
        num_batches=int(traindata.shape[0]/batch_size)
        
        # ==================== 新增：准备工作 ====================

        # 1. 实现基于属性的难负例挖掘 (Hard Negative Mining)
        print("Preparing for hard negative mining...")
        # 提取每个 seen 类别的唯一属性向量
        seen_class_attributes = {}
        for class_id in seen_class_indices:
            # 假设每个类别的所有样本属性都相同
            seen_class_attributes[class_id] = train_Y_by_class[class_id][0]

        # 将属性字典转换为一个列表，并保持ID和索引的对应关系
        # 注意：这里的 list(seen_class_indices) 保证了顺序
        ordered_class_ids = list(seen_class_indices)
        attribute_vectors = np.array([seen_class_attributes[cid] for cid in ordered_class_ids])

        # 计算属性向量之间的欧氏距离矩阵
        attribute_dist_matrix = squareform(pdist(attribute_vectors, 'euclidean'))

        # 为每个类别找出属性距离最近的其他类别（难负例）
        hard_negative_candidates = {}
        for i, class_id in enumerate(ordered_class_ids):
            # 获取当前类别与其他所有类别的距离
            distances = attribute_dist_matrix[i]
            # 对距离进行排序，并获取排序后的类别索引
            sorted_indices = np.argsort(distances)
            # 转换为实际的 class_id，并排除自己 (第一个是自己，距离为0)
            sorted_class_ids = [ordered_class_ids[j] for j in sorted_indices[1:]]
            hard_negative_candidates[class_id] = sorted_class_ids
        print("Hard negative candidates prepared.")


        # 2. 初始化类别质心 (Centroids)
        print("Initializing class centroids...")
        # 首先，我们需要用初始的编码器计算出所有训练样本的特征
        initial_features_by_class = {}
        for class_id in seen_class_indices:
            class_samples = train_X_by_class[class_id]
            initial_features_by_class[class_id] = self.encoder.predict(class_samples)

        # 然后计算每个类别的质心
        class_centroids = {}
        for class_id, features in initial_features_by_class.items():
            class_centroids[class_id] = tf.constant(np.mean(features, axis=0), dtype=tf.float32)
        print("Centroids initialized.")

        # 定义超参数：从最近的几个类别中选择负例
        N_hard_negatives = 5 # 例如，从属性最接近的5个类别中随机选一个作为负例

        # ==================== 准备工作结束 ====================
               
        nan_detected = False # 初始化NaN检测标志
        for epoch in range(epochs):
            
            # (可选但推荐) 在每个 epoch 开始时更新质心，以反映编码器的变化
            if epoch > 0:
                for class_id in seen_class_indices:
                    class_samples = train_X_by_class[class_id]
                    updated_features = self.encoder.predict(class_samples)
                    class_centroids[class_id] = tf.constant(np.mean(updated_features, axis=0), dtype=tf.float32)
            
            for batch_i in range(num_batches):
                
                start_i =batch_i * batch_size
                end_i=(batch_i + 1) * batch_size
                
                train_x=traindata[start_i:end_i]
                train_y=train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # Autoencoder and Classifier Training (Same as before)
                self.autoencoder.trainable = True
                self.c.trainable = True # Train C together with AE now
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                  feature, output_sample=self.autoencoder(train_x)
                  autoencoder_loss=mean_squared_error(train_x,output_sample)      

                  hidden_ouput_c,predict_attribute_c=self.c(feature)
                  c_loss=self.classification_loss(feature,train_y, hidden_ouput_c, predict_attribute_c)

                  total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                # 【修复点1】添加梯度裁剪
                grads_auto_c = [tf.clip_by_norm(g, 1.0) for g in grads_auto_c]
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                # Triplet Loss Metric Learning
                self.autoencoder.trainable = True # Encoder is part of metric learning
                self.c.trainable = True
                
                # Triplet Loss Metric Learning
                self.autoencoder.trainable = True # Encoder is part of metric learning
                self.c.trainable = True
                
                # === 新逻辑：准备正例和负例质心 ===
                positive_centroids_list = []
                negative_centroids_list = []
                
                for label in train_labels: # train_labels 是当前 batch 的类别标签
                    # 1. 获取正例质心
                    positive_centroids_list.append(class_centroids[label])

                    # 2. 获取难负例质心 (Hard Negative)
                    # 从预先计算好的候选列表中，随机选择一个难负例类别
                    candidate_list = hard_negative_candidates[label][:N_hard_negatives]
                    neg_class = np.random.choice(candidate_list)
                    negative_centroids_list.append(class_centroids[neg_class])

                # 将列表转换为张量
                positive_centroids = tf.stack(positive_centroids_list)
                negative_centroids = tf.stack(negative_centroids_list)
                
                with tf.GradientTape() as tape_m:
                    # 锚点特征保持不变
                    anchor_features = self.encoder(train_x) # train_x 是 anchor_samples
                    
                    # 计算 Triplet Loss (使用质心)
                    m_loss = self.triplet_loss(anchor_features, positive_centroids, negative_centroids)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                # 【修复点1】添加梯度裁剪
                grads_m = [tf.clip_by_norm(g, 1.0) for g in grads_m]
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # Discriminator Training (Same as before)
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False

                for _ in range(self.n_critic):
                  with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise,train_y])
                    real_feature = self.encoder(train_x)

                    real_validity = self.d([real_feature,train_y])
                    fake_validity = self.d([fake_feature,train_y])

                    # 【改进点4】添加数值稳定性检查
                    real_validity = tf.clip_by_value(real_validity, -10.0, 10.0)
                    fake_validity = tf.clip_by_value(fake_validity, -10.0, 10.0)

                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss = d_loss_real + d_loss_fake

                    # 确保判别器损失不会过大
                    d_loss = tf.clip_by_value(d_loss, 0.0, 10.0)

                  grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                  # 【修复点1】添加梯度裁剪
                  grads_d = [tf.clip_by_norm(g, 0.5) for g in grads_d if g is not None]
                  self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # Generator Training
                self.d.trainable = False               
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                  noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                  Fake_feature_g = self.g([noise_g,train_y])
                  Fake_validity_g = self.d([Fake_feature_g,train_y])

                  # 【改进点5】添加数值稳定性检查
                  Fake_validity_g = tf.clip_by_value(Fake_validity_g, -10.0, 10.0)
                  adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
                  adversarial_loss = tf.clip_by_value(adversarial_loss, -10.0, 10.0)

                  fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                  classification_loss = self.classification_loss(Fake_feature_g,train_y, fake_hidden_ouput_g, Fake_classification_g)
                  classification_loss = tf.clip_by_value(classification_loss, 0.0, 10.0)

                  # Triplet loss for Generator
                  # 锚点是生成的特征
                  g_anchor_features = Fake_feature_g

                  # 正例和负例我们使用上面为编码器准备好的同一批质心
                  # 这确保了生成器和编码器在同一个目标下对齐
                  g_positive_centroids = positive_centroids
                  g_negative_centroids = negative_centroids

                  triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_centroids, g_negative_centroids)

                  cycle_rank_loss = 0
                  if self.crl == True:
                    reconstructed_feature = self.g([noise_g, Fake_classification_g])

                    # For cycle rank, the "negative" is a feature from a different class
                    # We can reuse the negative samples from the batch for simplicity
                    negative_attributes = np.array([train_Y_by_class[np.random.choice([c for c in seen_class_indices if c != label])][0] for label in train_labels])
                    unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                    cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                    cycle_rank_loss = tf.clip_by_value(cycle_rank_loss, 0.0, 10.0)

                  # 【改进点3】使用调整后的损失权重
                  total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g + self.lambda_crl*cycle_rank_loss
                  total_loss = tf.clip_by_value(total_loss, -10.0, 10.0)  # 确保总损失不会过大

                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                # 【修复点1】添加梯度裁剪
                grads_g = [tf.clip_by_norm(g, 0.5) for g in grads_g if g is not None]
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time

                # TensorBoard日志记录
                if self.global_step % 10 == 0:
                    with self.summary_writer.as_default():
                        tf.summary.scalar('Loss/Autoencoder_Classifier', tf.reduce_mean(total_ac_loss), step=self.global_step)
                        tf.summary.scalar('Loss/Triplet_Metric', m_loss, step=self.global_step)
                        tf.summary.scalar('Loss/Discriminator', d_loss, step=self.global_step)
                        tf.summary.scalar('Loss/Generator', tf.reduce_mean(total_loss), step=self.global_step)
                        tf.summary.scalar('Training/Learning_Rate', self.g_optimizer.learning_rate, step=self.global_step)

                # 详细的日志输出
                log_msg = (f"[Epoch {epoch+1}/{epochs}][Batch {batch_i+1}/{num_batches}] "
                          f"AE+C Loss: {tf.reduce_mean(total_ac_loss):.4f}, "
                          f"M Loss: {m_loss:.4f}, "
                          f"D Loss: {d_loss:.4f}, "
                          f"G Loss: {tf.reduce_mean(total_loss):.4f}, "
                          f"Time: {elapsed_time}")

                self.logger.info(log_msg)

                # 保持原有的print输出以兼容
                print ("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][D loss: %f][G loss %05f ]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                   tf.reduce_mean(total_ac_loss),
                     m_loss,
                     d_loss,
                     tf.reduce_mean(total_loss),
                     elapsed_time))

                self.global_step += 1
               
                # 【修复点2】在每个batch后检查损失是否为NaN，添加详细诊断
                if tf.math.is_nan(m_loss).numpy().any() or tf.math.is_nan(d_loss).numpy().any() or tf.math.is_nan(total_loss).numpy().any():
                    self.logger.error(f"NaN detected in losses at Epoch {epoch}, Batch {batch_i}")
                    self.logger.error(f"  - M Loss: {m_loss}")
                    self.logger.error(f"  - D Loss: {d_loss}")
                    self.logger.error(f"  - G Total Loss: {total_loss}")
                    self.logger.error(f"  - AE+C Loss: {tf.reduce_mean(total_ac_loss)}")

                    print(f"\n[ERROR] NaN detected in losses at Epoch {epoch}, Batch {batch_i}. Stopping training.")
                    print(f"  - M Loss: {m_loss}")
                    print(f"  - D Loss: {d_loss}")
                    print(f"  - G Total Loss: {total_loss}")

                    if log_file:
                        log_file.write(f"\n[ERROR] NaN detected at Epoch {epoch}, Batch {batch_i}. Training stopped.\n")
                        log_file.write(f"  - M Loss: {m_loss}\n")
                        log_file.write(f"  - D Loss: {d_loss}\n")
                        log_file.write(f"  - G Total Loss: {total_loss}\n")
                        log_file.flush()
                    # 设置一个标志来跳出外层循环
                    nan_detected = True
                    break # 退出内部的batch循环

            # 在每个epoch结束后，检查是否有NaN发生
            if 'nan_detected' in locals() and nan_detected:
                break # 退出外部的epoch循环

            if epoch % 1 == 0:
                self.logger.info(f"Epoch {epoch+1} 完成，开始评估...")
                accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp = feature_generation_and_diagnosis(2000,testdata,test_attributelabel,self.autoencoder,self.g, self.c, test_class_indices)

                accuracy_list_1.append(accuracy_lsvm)
                accuracy_list_2.append(accuracy_nrf)
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                # 更新最佳准确率
                if accuracy_lsvm > self.best_accs['lsvm']:
                    self.best_accs['lsvm'] = accuracy_lsvm
                if accuracy_nrf > self.best_accs['nrf']:
                    self.best_accs['nrf'] = accuracy_nrf
                if accuracy_pnb > self.best_accs['pnb']:
                    self.best_accs['pnb'] = accuracy_pnb
                if accuracy_mlp > self.best_accs['mlp']:
                    self.best_accs['mlp'] = accuracy_mlp

                current_best = max(accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp)
                if current_best > self.overall_best_acc:
                    self.overall_best_acc = current_best

                # TensorBoard记录准确率
                with self.summary_writer.as_default():
                    tf.summary.scalar('Accuracy/LSVM_current', accuracy_lsvm, step=epoch)
                    tf.summary.scalar('Accuracy/NRF_current', accuracy_nrf, step=epoch)
                    tf.summary.scalar('Accuracy/PNB_current', accuracy_pnb, step=epoch)
                    tf.summary.scalar('Accuracy/MLP_current', accuracy_mlp, step=epoch)
                    tf.summary.scalar('Accuracy/Best_LSVM', self.best_accs['lsvm'], step=epoch)
                    tf.summary.scalar('Accuracy/Best_NRF', self.best_accs['nrf'], step=epoch)
                    tf.summary.scalar('Accuracy/Best_PNB', self.best_accs['pnb'], step=epoch)
                    tf.summary.scalar('Accuracy/Best_MLP', self.best_accs['mlp'], step=epoch)
                    tf.summary.scalar('Accuracy/Overall_Best', self.overall_best_acc, step=epoch)

                # 详细的Epoch总结日志
                self.logger.info(f"--- Epoch {epoch+1} Summary ---")
                self.logger.info(f"  - Current Accs: [LSVM: {accuracy_lsvm:.4f}] [NRF: {accuracy_nrf:.4f}] [PNB: {accuracy_pnb:.4f}] [MLP: {accuracy_mlp:.4f}]")
                self.logger.info(f"  - Best Accs:    [LSVM: {self.best_accs['lsvm']:.4f}] [NRF: {self.best_accs['nrf']:.4f}] [PNB: {self.best_accs['pnb']:.4f}] [MLP: {self.best_accs['mlp']:.4f}]")
                self.logger.info(f"  - Overall Best Accuracy: {self.overall_best_acc:.4f}")
                self.logger.info("-" * 40)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                  %(epoch, epochs,max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)))
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)])

        # 训练完成日志
        end_time = datetime.datetime.now()
        total_time = end_time - start_time

        self.logger.info("=" * 60)
        self.logger.info("训练完成!")
        self.logger.info(f"总训练时间: {total_time}")
        self.logger.info(f"最佳准确率: {best_accuracy:.4f}")
        self.logger.info(f"各分类器最佳准确率:")
        self.logger.info(f"  - LSVM: {self.best_accs['lsvm']:.4f}")
        self.logger.info(f"  - NRF:  {self.best_accs['nrf']:.4f}")
        self.logger.info(f"  - PNB:  {self.best_accs['pnb']:.4f}")
        self.logger.info(f"  - MLP:  {self.best_accs['mlp']:.4f}")
        self.logger.info(f"日志和TensorBoard文件保存在: {self.log_dir}")
        self.logger.info("=" * 60)

        # 最终TensorBoard记录
        with self.summary_writer.as_default():
            tf.summary.scalar('Final/Best_Accuracy', best_accuracy, step=epochs)
            tf.summary.scalar('Final/Training_Time_Minutes', total_time.total_seconds()/60, step=epochs)

        print('finished! best_acc:{:.4f}'.format(best_accuracy))
        if log_file:
            log_file.write(f'finished! best_acc:{best_accuracy:.4f}\n')
            log_file.flush()

        return best_accuracy, self.best_accs, self.log_dir
                
if __name__ == '__main__':
    # =============================================================
    # 配置区域：只需要修改这里的TARGET_GROUP即可切换实验组别
    # =============================================================
    TARGET_GROUP = 'A'#可选: 'A', 'B', 'C', 'D', 'E'
    # =============================================================
    
    # 分组配置
    GROUP_CONFIGS = {
        'A': [1, 6, 14],   # 测试类别: [1, 6, 14] 
        'B': [4, 7, 10],   # 测试类别: [4, 7, 10]
        'C': [8, 11, 12],  # 测试类别: [8, 11, 12]
        'D': [2, 3, 5],    # 测试类别: [2, 3, 5]
        'E': [9, 13, 15],  # 测试类别: [9, 13, 15]
    }
    
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + f"_triplet_improved_Group{TARGET_GROUP}.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"开始运行改进版 Group {TARGET_GROUP} 实验，测试类别: {GROUP_CONFIGS[TARGET_GROUP]}")
    print(f"训练开始，日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 训练日志 (改进版Triplet架构 - Group {TARGET_GROUP})\n\n")
        log_file.write(f"**改进说明**: 引入后期属性调制机制 + 调整损失权重\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**实验组别**: Group {TARGET_GROUP}\n")
        log_file.write(f"**测试类别**: {GROUP_CONFIGS[TARGET_GROUP]}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot()
        best_acc, best_accs, log_dir = gan.train(epochs=2000, batch_size=256, log_file=log_file, test_class_indices=GROUP_CONFIGS[TARGET_GROUP], group_name=TARGET_GROUP)

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")
        log_file.write(f"**最佳准确率**: {best_acc:.4f}\n")
        log_file.write(f"**详细日志目录**: {log_dir}\n")
        log_file.write(f"**TensorBoard命令**: `tensorboard --logdir {log_dir}`\n")

    print(f"训练完成!")
    print(f"最佳准确率: {best_acc:.4f}")
    print(f"Markdown日志已保存至: {log_filename}")
    print(f"详细日志和TensorBoard文件保存在: {log_dir}")
    print(f"查看TensorBoard: tensorboard --logdir {log_dir}")