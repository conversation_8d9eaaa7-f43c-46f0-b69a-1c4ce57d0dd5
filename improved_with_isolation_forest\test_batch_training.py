#!/usr/bin/env python3
"""
测试批量训练功能的快速验证脚本
运行少量epoch来验证批量训练是否正常工作
"""

import os
import sys
import subprocess
import time
import logging
from datetime import datetime

def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('BatchTestIF')

def test_single_batch_experiment():
    """测试单个批量实验"""
    logger = logging.getLogger('BatchTestIF')
    
    logger.info("🧪 测试批量训练功能...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['BATCH_MODE'] = '1'
    
    # 构建命令 - 只训练2个epoch进行测试
    cmd = [
        sys.executable,
        "run_enhanced_baseline_with_if.py",
        "--group", "A",
        "--epochs", "2",
        "--batch_size", "120",
        "--use_if"
    ]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    start_time = time.time()
    
    try:
        # 运行训练
        process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True,
            env=env
        )

        # 实时读取输出
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # 在批量模式下，我们应该看到较少的重复输出
                output_lines.append(output)
                logger.info(f"[Batch Test] {output.strip()}")

        return_code = process.poll()
        end_time = time.time()
        duration = end_time - start_time
        
        if return_code == 0:
            logger.info(f"✅ 批量训练测试成功完成，耗时: {duration:.2f}秒")
            
            # 检查是否有重复日志
            full_output = ''.join(output_lines)
            duplicate_count = full_output.count('[Group A baseline]')
            
            if duplicate_count == 0:
                logger.info("✅ 没有发现重复日志输出")
            else:
                logger.warning(f"⚠️ 发现 {duplicate_count} 条重复日志")
            
            return True
        else:
            logger.error(f"❌ 批量训练测试失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 批量训练测试出现异常: {e}")
        return False

def test_logging_isolation():
    """测试日志隔离功能"""
    logger = logging.getLogger('BatchTestIF')
    
    logger.info("🧪 测试日志隔离功能...")
    
    # 测试1: 非批量模式
    logger.info("测试非批量模式...")
    os.environ.pop('BATCH_MODE', None)  # 确保没有设置
    
    try:
        from run_enhanced_baseline_with_if import BaselineTrainerWithIF
        trainer1 = BaselineTrainerWithIF(
            group='A',
            epochs=1,
            batch_size=120,
            use_isolation_forest=False,
            log_dir_base="logs/test_isolation"
        )
        
        # 检查logger配置
        logger1 = trainer1.logger
        console_handlers = [h for h in logger1.handlers if hasattr(h, 'stream')]
        
        if console_handlers:
            logger.info("✅ 非批量模式：正确配置了控制台输出")
        else:
            logger.warning("⚠️ 非批量模式：未找到控制台处理器")
        
        # 测试2: 批量模式
        logger.info("测试批量模式...")
        os.environ['BATCH_MODE'] = '1'
        
        trainer2 = BaselineTrainerWithIF(
            group='B',
            epochs=1,
            batch_size=120,
            use_isolation_forest=False,
            log_dir_base="logs/test_isolation"
        )
        
        logger2 = trainer2.logger
        console_handlers2 = [h for h in logger2.handlers if hasattr(h, 'stream')]
        
        if not console_handlers2:
            logger.info("✅ 批量模式：正确禁用了控制台输出")
        else:
            logger.warning("⚠️ 批量模式：仍然有控制台处理器")
        
        # 清理环境变量
        os.environ.pop('BATCH_MODE', None)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 日志隔离测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始批量训练修复效果验证")
    print("="*60)
    
    logger = setup_logging()
    
    tests = [
        ("日志隔离功能", test_logging_isolation),
        ("批量训练功能", test_single_batch_experiment)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"🔍 运行测试: {test_name}")
        print(f"{'='*40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            logger.error(f"💥 {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 批量训练测试结果总结")
    print(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有批量训练测试通过！可以开始完整训练了。")
        print("\n📋 建议的下一步操作：")
        print("1. 运行完整的200轮训练：")
        print("   python run_enhanced_baseline_with_if.py --group A --epochs 200 --batch_size 120 --use_if")
        print("\n2. 或者运行批量训练所有组别：")
        print("   python run_all_groups_with_if.py")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
