#!/usr/bin/env python3
"""
测试修复效果的快速验证脚本
运行少量epoch来验证日志和损失值是否正常
"""

import os
import sys
import logging
from datetime import datetime

# 添加路径
sys.path.append('scripts')
sys.path.append('improved_with_isolation_forest/scripts')

def test_single_training():
    """测试单个训练脚本"""
    print("🧪 测试单个训练脚本...")
    
    from run_enhanced_baseline_with_if import BaselineTrainerWithIF
    
    # 创建训练器，只训练2个epoch进行测试
    trainer = BaselineTrainerWithIF(
        group='A',
        epochs=2,
        batch_size=120,
        use_isolation_forest=True,
        contamination=0.1,
        log_dir_base="logs/test_fixes"
    )
    
    print("✅ 训练器创建成功")
    print(f"📁 日志目录: {trainer.log_dir}")
    
    # 运行训练
    try:
        trainer.train()
        print("✅ 训练完成，无异常")
        return True
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False

def test_logging_setup():
    """测试日志配置"""
    print("🧪 测试日志配置...")
    
    # 模拟批量模式
    os.environ['BATCH_MODE'] = '1'
    
    from run_enhanced_baseline_with_if import BaselineTrainerWithIF
    
    trainer = BaselineTrainerWithIF(
        group='A',
        epochs=1,
        batch_size=120,
        use_isolation_forest=False,
        log_dir_base="logs/test_logging"
    )
    
    # 检查logger配置
    logger = trainer.logger
    print(f"Logger名称: {logger.name}")
    print(f"Logger级别: {logger.level}")
    print(f"处理器数量: {len(logger.handlers)}")
    print(f"传播设置: {logger.propagate}")
    
    # 测试日志输出
    logger.info("这是一条测试日志")
    
    # 清理环境变量
    del os.environ['BATCH_MODE']
    
    print("✅ 日志配置测试完成")
    return True

def test_loss_normalization():
    """测试损失值归一化"""
    print("🧪 测试损失值归一化...")
    
    import tensorflow as tf
    import numpy as np
    from tensorflow.keras.losses import mean_squared_error
    
    # 创建测试数据
    test_input = np.random.normal(0, 100, (10, 52))  # 大范围数据
    test_output = np.random.normal(0, 100, (10, 52))
    
    # 原始损失计算
    original_loss = tf.reduce_mean(mean_squared_error(test_input, test_output))
    print(f"原始损失值: {original_loss:.4f}")
    
    # 归一化后的损失计算
    input_normalized = (test_input - tf.reduce_min(test_input)) / (tf.reduce_max(test_input) - tf.reduce_min(test_input) + 1e-8)
    output_normalized = (test_output - tf.reduce_min(test_output)) / (tf.reduce_max(test_output) - tf.reduce_min(test_output) + 1e-8)
    normalized_loss = tf.reduce_mean(mean_squared_error(input_normalized, output_normalized))
    print(f"归一化损失值: {normalized_loss:.4f}")
    
    if normalized_loss < original_loss:
        print("✅ 损失值归一化有效")
        return True
    else:
        print("⚠️ 损失值归一化效果不明显")
        return False

def main():
    """主测试函数"""
    print("🧪 开始修复效果验证")
    print("="*60)
    
    tests = [
        ("日志配置", test_logging_setup),
        ("损失值归一化", test_loss_normalization),
        ("单个训练脚本", test_single_training)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"🔍 运行测试: {test_name}")
        print(f"{'='*40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"💥 {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试结果总结")
    print(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复效果良好。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
