"""
集成孤立森林过滤机制的增强基线训练脚本
基于原始run_enhanced_baseline.py，添加了生成样本质量过滤功能
"""

import os
import argparse
import logging
from datetime import datetime
import tensorflow as tf
import numpy as np
import sys

# 添加路径
sys.path.append('scripts')
sys.path.append('improved_with_isolation_forest/scripts')

# 导入模块
from acgan_fg_with_isolation_forest import Zero_shot_with_IF, RandomWeightedAverage
from data_loader import load_data_for_group
from evaluation_with_if import feature_generation_and_diagnosis_with_if
from tensorflow.keras.losses import mean_squared_error

class BaselineTrainerWithIF:
    """
    集成孤立森林过滤机制的基线训练器
    """
    def __init__(self, group: str, epochs: int, batch_size: int, 
                 use_isolation_forest: bool = True, contamination: float = 0.1,
                 log_dir_base: str = "logs/isolation_forest"):
        self.group = group
        self.epochs = epochs
        self.batch_size = batch_size
        self.use_isolation_forest = use_isolation_forest
        self.contamination = contamination
        
        # 创建带时间戳和组别的日志目录
        current_time = datetime.now().strftime("%Y%m%d-%H%M%S")
        filter_suffix = "_with_IF" if use_isolation_forest else "_baseline"
        self.log_dir = os.path.join(log_dir_base, f"Group-{self.group}{filter_suffix}_{current_time}")
        os.makedirs(self.log_dir, exist_ok=True)
        
        self._setup_logging()
        self.logger.info(f"初始化训练器，组别: {self.group}, 使用孤立森林: {use_isolation_forest}")
        self.logger.info(f"日志目录: {self.log_dir}")
        
        # 初始化模型
        self.model = Zero_shot_with_IF(
            use_isolation_forest=use_isolation_forest,
            contamination=contamination
        )
        
        # 初始化 TensorBoard
        self.summary_writer = tf.summary.create_file_writer(self.log_dir)
        self.global_step = 0
        
        # 用于跟踪历史最佳准确率
        self.best_accs = {'lsvm': 0.0, 'nrf': 0.0, 'pnb': 0.0, 'mlp': 0.0}
        self.overall_best_acc = 0.0

    def _setup_logging(self):
        """设置日志，避免重复输出"""
        # 创建专用的logger，避免与父进程日志冲突
        self.logger = logging.getLogger(f'BaselineTrainerWithIF_{self.group}')

        # 如果logger已经有处理器，说明已经配置过了，直接返回
        if self.logger.handlers:
            return

        # 设置日志级别
        self.logger.setLevel(logging.INFO)

        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

        # 文件处理器
        file_handler = logging.FileHandler(os.path.join(self.log_dir, 'training.log'))
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

        # 控制台处理器（只在非批量模式下添加）
        if not os.environ.get('BATCH_MODE'):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)

        # 防止向上传播到root logger
        self.logger.propagate = False

    def train(self):
        """执行完整的训练流程，集成孤立森林过滤"""
        self.logger.info(f"开始为组别 {self.group} 进行训练，共 {self.epochs} 轮。")
        self.logger.info(f"孤立森林过滤: {'启用' if self.use_isolation_forest else '禁用'}")
        if self.use_isolation_forest:
            self.logger.info(f"异常样本比例: {self.contamination}")

        # 设置随机种子以确保可重现性
        tf.random.set_seed(42)
        np.random.seed(42)

        # 减少TensorFlow数据管道警告
        import os
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'  # 只显示错误和警告
        tf.get_logger().setLevel('ERROR')  # 减少TensorFlow日志

        traindata, train_attributelabel, testdata, test_attributelabel, unseen_classes = load_data_for_group(self.group)
        
        start_time = datetime.now()
        
        accuracy_list_1, accuracy_list_2, accuracy_list_3, accuracy_list_4 = [], [], [], []
        
        valid = -np.ones((self.batch_size, 1), dtype=np.float32)
        fake = np.ones((self.batch_size, 1), dtype=np.float32)
        similar_truth = np.ones((self.batch_size, 1), dtype=np.float32)
        unsimilar_truth = np.zeros((self.batch_size, 1), dtype=np.float32)
        
        num_batches = int(traindata.shape[0] / self.batch_size)
               
        for epoch in range(self.epochs):
            epoch_start_time = datetime.now()
            for batch_i in range(num_batches):
                
                start_i = batch_i * self.batch_size
                end_i = (batch_i + 1) * self.batch_size
                
                train_x = traindata[start_i:end_i]
                train_y = train_attributelabel[start_i:end_i]
                
                # === 训练 Autoencoder ===
                with tf.GradientTape() as tape_auto:
                    feature, output_sample = self.model.autoencoder(train_x)
                    # 归一化输入数据到[0,1]范围，避免损失值过大
                    train_x_normalized = (train_x - tf.reduce_min(train_x)) / (tf.reduce_max(train_x) - tf.reduce_min(train_x) + 1e-8)
                    output_normalized = (output_sample - tf.reduce_min(output_sample)) / (tf.reduce_max(output_sample) - tf.reduce_min(output_sample) + 1e-8)
                    autoencoder_loss = tf.reduce_mean(mean_squared_error(train_x_normalized, output_normalized))
                grads_autoencoder = tape_auto.gradient(autoencoder_loss, self.model.autoencoder.trainable_weights)
                # 梯度裁剪，防止梯度爆炸
                grads_autoencoder = [tf.clip_by_norm(grad, 1.0) if grad is not None else grad for grad in grads_autoencoder]
                self.model.autoencoder_optimizer.apply_gradients(zip(grads_autoencoder, self.model.autoencoder.trainable_weights))

                # === 训练 Classifier ===
                with tf.GradientTape() as tape_c:
                    feature_c, _ = self.model.autoencoder(train_x)
                    hidden_output_c, predict_attribute_c = self.model.c(feature_c)
                    c_loss = tf.reduce_mean(self.model.classification_loss(feature_c, train_y, hidden_output_c, predict_attribute_c))
                grads_c = tape_c.gradient(c_loss, self.model.c.trainable_weights)
                grads_c = [tf.clip_by_norm(grad, 1.0) if grad is not None else grad for grad in grads_c]
                self.model.c_optimizer.apply_gradients(zip(grads_c, self.model.c.trainable_weights))

                # === 训练 Comparator ===
                self.model.autoencoder.trainable = False
                self.model.c.trainable = False
                self.model.m.trainable = True
                self.model.d.trainable = False
                self.model.g.trainable = False

                position = int(start_i / 480)
                delete_start = position * 480
                delete_end = (position + 1) * 480
                new_traindata = np.delete(traindata, slice(delete_start, delete_end), axis=0)
                new_train_attribute_label = np.delete(train_attributelabel, slice(delete_start, delete_end), axis=0)
                random_indices = np.random.choice(new_traindata.shape[0], size=self.batch_size, replace=False)
                selected_train_x = new_traindata[random_indices]

                m_loss_1 = self.model.m_model.train_on_batch([train_x, train_x], [similar_truth])
                m_loss_2 = self.model.m_model.train_on_batch([train_x, selected_train_x], [unsimilar_truth])
                m_loss = (m_loss_1 + m_loss_2) / 2

                # === 训练 Discriminator ===
                self.model.autoencoder.trainable = False
                self.model.c.trainable = False
                self.model.m.trainable = False
                self.model.d.trainable = True
                self.model.g.trainable = False

                for _ in range(self.model.n_critic):
                    with tf.GradientTape(persistent=True) as tape_d:
                        Noise_shape = (self.batch_size, 50, 1)
                        noise = tf.random.normal(shape=Noise_shape)
                        fake_feature = self.model.g([noise, train_y])
                        real_feature, decoded = self.model.autoencoder(train_x)
                        interpolated_feature = RandomWeightedAverage()([real_feature, fake_feature])

                        real_validity = self.model.d([real_feature, train_y])
                        fake_validity = self.model.d([fake_feature, train_y])
                        interploted_validity = self.model.d([interpolated_feature, train_y])

                        d_loss_real = self.model.wasserstein_loss(valid, real_validity)
                        d_loss_fake = self.model.wasserstein_loss(fake, fake_validity)

                        gradients = tape_d.gradient(interploted_validity, interpolated_feature)
                        gradient_penalty = self.model.gradient_penalty_loss(gradients)

                        d_loss = d_loss_real + d_loss_fake + self.model.LAMBDA_GP * gradient_penalty

                    average_d_loss = tf.reduce_mean(d_loss)
                    grads_d = tape_d.gradient(d_loss, self.model.d.trainable_weights)
                    grads_d = [tf.clip_by_norm(grad, 1.0) if grad is not None else grad for grad in grads_d]
                    self.model.d_optimizer.apply_gradients(zip(grads_d, self.model.d.trainable_weights))
                    del tape_d

                # === 训练 Generator ===
                self.model.autoencoder.trainable = False
                self.model.c.trainable = False
                self.model.m.trainable = False
                self.model.d.trainable = False
                self.model.g.trainable = True

                Noise_shape_g = (self.batch_size, self.model.latent_dim, 1)
                noise_g = tf.random.normal(shape=Noise_shape_g)

                base_indices = np.random.choice(480, size=self.batch_size, replace=False)
                all_indices = base_indices[None,:] + 480 * np.arange(11)[:,None]

                homogeneous_feature, new_sample_1 = self.model.autoencoder(train_x)

                heterogeneous_features, new_samples = zip(*[
                    self.model.autoencoder(new_traindata[indices])
                    for indices in all_indices])

                heterogeneous_attributes = [new_train_attribute_label[indices] for indices in all_indices]

                with tf.GradientTape(persistent=True) as tape_g:
                    fake_feature_g = self.model.g([noise_g, train_y])
                    fake_validity_g = self.model.d([fake_feature_g, train_y])
                    adversarial_loss = self.model.wasserstein_loss(valid, fake_validity_g)

                    fake_hidden_output_g, fake_classification_g = self.model.c(fake_feature_g)
                    classification_loss = self.model.classification_loss(fake_feature_g, train_y, fake_hidden_output_g, fake_classification_g)

                    comparison_loss_simi = tf.keras.losses.binary_crossentropy(
                        similar_truth, self.model.m([fake_feature_g, homogeneous_feature]))

                    comparison_loss_unsimi = tf.reduce_sum([
                        tf.keras.losses.binary_crossentropy(
                            unsimilar_truth,
                            self.model.m([fake_feature_g, heterogeneous_features[i]])
                        ) for i in range(11)
                    ], axis=0)

                    cycle_rank_loss = 0
                    if self.model.crl == True:
                        reconstructed_feature = self.model.g([noise_g, fake_classification_g])
                        fake_feature_g_unsimi = [
                            self.model.g([noise_g, heterogeneous_attributes[i]])
                            for i in range(11)
                        ]
                        cycle_rank_losses = [
                            self.model.cycle_rank_loss(
                                fake_feature_g,
                                reconstructed_feature,
                                fake_feature_g_unsimi[i],
                                similar_truth
                            ) for i in range(11)
                        ]
                        cycle_rank_loss = tf.reduce_mean(cycle_rank_losses, axis=0)

                    total_loss = (adversarial_loss +
                                  self.model.lambda_cla * classification_loss +
                                  self.model.lambda_cms * comparison_loss_simi +
                                  self.model.lambda_cms * comparison_loss_unsimi +
                                  self.model.lambda_crl * cycle_rank_loss)

                average_g_loss = tf.reduce_mean(total_loss)
                grads_g = tape_g.gradient(total_loss, self.model.g.trainable_weights)
                grads_g = [tf.clip_by_norm(grad, 1.0) if grad is not None else grad for grad in grads_g]
                self.model.g_optimizer.apply_gradients(zip(grads_g, self.model.g.trainable_weights))

                del tape_g

                # --- 日志和 TensorBoard ---
                if self.global_step % 10 == 0:
                    with self.summary_writer.as_default():
                        tf.summary.scalar('Loss/Autoencoder', autoencoder_loss, step=self.global_step)
                        tf.summary.scalar('Loss/Classifier', c_loss, step=self.global_step)
                        tf.summary.scalar('Loss/Comparator', m_loss, step=self.global_step)
                        tf.summary.scalar('Loss/Discriminator', average_d_loss, step=self.global_step)
                        tf.summary.scalar('Loss/Generator', average_g_loss, step=self.global_step)
                
                # --- 每个Batch都输出日志 ---
                log_msg = (f"[Epoch {epoch+1}/{self.epochs}][Batch {batch_i+1}/{num_batches}] "
                           f"D Loss: {average_d_loss:.4f}, G Loss: {average_g_loss:.4f}, "
                           f"C Loss: {c_loss:.4f}, M Loss: {m_loss:.4f}, AE Loss: {autoencoder_loss:.4f}")
                self.logger.info(log_msg)

                self.global_step += 1

            # --- Epoch 结束后的评估 - 每轮都评估 ---
            self.logger.info(f"Epoch {epoch+1} completed, starting evaluation...")
            acc_lsvm, acc_nrf, acc_pnb, acc_mlp = feature_generation_and_diagnosis_with_if(
                1000, testdata, test_attributelabel, self.model.autoencoder, self.model.g, self.model.c,
                unseen_classes, use_isolation_forest=self.use_isolation_forest,
                contamination=self.contamination, tensorboard_writer=self.summary_writer, step=epoch
            )
            
            # 更新历史最佳
            self.best_accs['lsvm'] = max(self.best_accs['lsvm'], acc_lsvm)
            self.best_accs['nrf'] = max(self.best_accs['nrf'], acc_nrf)
            self.best_accs['pnb'] = max(self.best_accs['pnb'], acc_pnb)
            self.best_accs['mlp'] = max(self.best_accs['mlp'], acc_mlp)
            current_best = max(acc_lsvm, acc_nrf, acc_pnb, acc_mlp)
            self.overall_best_acc = max(self.overall_best_acc, current_best)

            with self.summary_writer.as_default():
                tf.summary.scalar('Accuracy/LSVM_current', acc_lsvm, step=epoch)
                tf.summary.scalar('Accuracy/NRF_current', acc_nrf, step=epoch)
                tf.summary.scalar('Accuracy/PNB_current', acc_pnb, step=epoch)
                tf.summary.scalar('Accuracy/MLP_current', acc_mlp, step=epoch)
                tf.summary.scalar('Accuracy/Best_MLP', self.best_accs['mlp'], step=epoch)
                tf.summary.scalar('Accuracy/Overall_Best', self.overall_best_acc, step=epoch)

            # --- 输出Epoch总结 ---
            filter_status = "with IF" if self.use_isolation_forest else "baseline"
            self.logger.info(f"--- Epoch {epoch+1} Summary ({filter_status}) ---")
            self.logger.info(f"  - Current Accs: [LSVM: {acc_lsvm:.4f}] [NRF: {acc_nrf:.4f}] [PNB: {acc_pnb:.4f}] [MLP: {acc_mlp:.4f}]")
            self.logger.info(f"  - Best Accs:    [LSVM: {self.best_accs['lsvm']:.4f}] [NRF: {self.best_accs['nrf']:.4f}] [PNB: {self.best_accs['pnb']:.4f}] [MLP: {self.best_accs['mlp']:.4f}]")
            self.logger.info(f"  - Overall Best Accuracy: {self.overall_best_acc:.4f}")
            self.logger.info("-" * (23 + len(str(epoch+1)) + len(filter_status) + 8))
            
        self.logger.info("Training completed!")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="集成孤立森林过滤机制的ACGAN-FG训练脚本")
    parser.add_argument('--group', type=str, required=True, choices=['A', 'B', 'C', 'D', 'E'], help='要训练和测试的数据组别')
    parser.add_argument('--epochs', type=int, default=200, help='训练的轮次')
    parser.add_argument('--batch_size', type=int, default=120, help='批次大小')
    parser.add_argument('--use_if', action='store_true', help='是否使用孤立森林过滤')
    parser.add_argument('--no_if', action='store_true', help='禁用孤立森林过滤')
    parser.add_argument('--contamination', type=float, default=0.1, help='孤立森林异常比例')
    
    args = parser.parse_args()

    # 确定是否使用孤立森林
    use_if = args.use_if and not args.no_if

    trainer = BaselineTrainerWithIF(
        group=args.group,
        epochs=args.epochs,
        batch_size=args.batch_size,
        use_isolation_forest=use_if,
        contamination=args.contamination
    )
    
    trainer.train()
