{"timestamp": "2025-07-31T03:35:16.032887", "total_groups": 5, "successful_groups": 5, "failed_groups": 0, "results": [{"group": "A", "status": "success", "duration_hours": 6.424300542076429, "best_accuracy": 0.7038, "stdout": ", 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.5903\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3333\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3333\n所有分类器训练完成！\nloading data...\ntest classes: [1, 6, 14]\ntrain classes: [ 2  3  4  5  7  8  9 10 11 12 13 15]\n为类别 1 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 6 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 14 生成 2000 个特征, 属性形状: (2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3260\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3340\n训练RandomForest...\nRandomForest完成，准确率: 0.3333\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3319\n所有分类器训练完成！\nloading data...\ntest classes: [1, 6, 14]\ntrain classes: [ 2  3  4  5  7  8  9 10 11 12 13 15]\n为类别 1 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 6 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 14 生成 2000 个特征, 属性形状: (2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3333\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3333\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3309\n所有分类器训练完成！\n", "stderr": ""}, {"group": "B", "status": "success", "duration_hours": 5.874384071363343, "best_accuracy": 0.4542, "stdout": ", 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3292\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3087\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3292\n所有分类器训练完成！\nloading data...\ntest classes: [4, 7, 10]\ntrain classes: [ 1  2  3  5  6  8  9 11 12 13 14 15]\n为类别 4 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 7 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 10 生成 2000 个特征, 属性形状: (2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3587\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3306\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3514\n所有分类器训练完成！\nloading data...\ntest classes: [4, 7, 10]\ntrain classes: [ 1  2  3  5  6  8  9 11 12 13 14 15]\n为类别 4 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 7 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 10 生成 2000 个特征, 属性形状: (2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3333\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3896\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3337\n所有分类器训练完成！\n", "stderr": ""}, {"group": "C", "status": "success", "duration_hours": 6.03645248512427, "best_accuracy": 0.4486, "stdout": ")\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3333\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3941\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3333\n所有分类器训练完成！\nloading data...\ntest classes: [8, 11, 12]\ntrain classes: [ 1  2  3  4  5  6  7  9 10 13 14 15]\n为类别 8 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 11 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 12 生成 2000 个特征, 属性形状: (2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3333\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3198\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3253\n所有分类器训练完成！\nloading data...\ntest classes: [8, 11, 12]\ntrain classes: [ 1  2  3  4  5  6  7  9 10 13 14 15]\n为类别 8 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 11 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 12 生成 2000 个特征, 属性形状: (2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3333\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3243\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3333\n所有分类器训练完成！\n", "stderr": ""}, {"group": "D", "status": "success", "duration_hours": 6.034622109201219, "best_accuracy": 0.6035, "stdout": "2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3993\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3333\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3333\n所有分类器训练完成！\nloading data...\ntest classes: [2, 3, 5]\ntrain classes: [ 1  4  6  7  8  9 10 11 12 13 14 15]\n为类别 2 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 3 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 5 生成 2000 个特征, 属性形状: (2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.5115\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3438\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3333\n所有分类器训练完成！\nloading data...\ntest classes: [2, 3, 5]\ntrain classes: [ 1  4  6  7  8  9 10 11 12 13 14 15]\n为类别 2 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 3 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 5 生成 2000 个特征, 属性形状: (2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3333\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3333\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3333\n所有分类器训练完成！\n", "stderr": ""}, {"group": "E", "status": "success", "duration_hours": 6.0602928426530625, "best_accuracy": 0.5066, "stdout": ")\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3333\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3333\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3417\n所有分类器训练完成！\nloading data...\ntest classes: [9, 13, 15]\ntrain classes: [ 1  2  3  4  5  6  7  8 10 11 12 14]\n为类别 9 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 13 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 15 生成 2000 个特征, 属性形状: (2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3333\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3333\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3333\n所有分类器训练完成！\nloading data...\ntest classes: [9, 13, 15]\ntrain classes: [ 1  2  3  4  5  6  7  8 10 11 12 14]\n为类别 9 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 13 生成 2000 个特征, 属性形状: (2000, 20)\n为类别 15 生成 2000 个特征, 属性形状: (2000, 20)\n开始快速分类器训练...\n训练LinearSVM...\nLinearSVM完成，准确率: 0.3333\n训练GaussianNB...\nGaussianNB完成，准确率: 0.3333\n训练RandomForest...\nRandomForest完成，准确率: 0.3333\n训练MLPClassifier...\nMLPClassifier完成，准确率: 0.3375\n所有分类器训练完成！\n", "stderr": ""}]}