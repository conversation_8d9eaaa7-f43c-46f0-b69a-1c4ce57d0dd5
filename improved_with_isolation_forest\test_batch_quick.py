#!/usr/bin/env python3
"""
快速测试批量训练的日志修复效果
只运行2个epoch来验证日志输出是否正常
"""

import os
import sys
import subprocess
import time
import logging
from datetime import datetime

def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('QuickBatchTest')

def test_batch_logging():
    """测试批量训练的日志输出"""
    logger = setup_logging()
    
    logger.info("🧪 测试批量训练日志修复效果...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['BATCH_MODE'] = '1'
    
    # 构建命令 - 只训练2个epoch进行测试
    cmd = [
        sys.executable,
        "run_enhanced_baseline_with_if.py",
        "--group", "A",
        "--epochs", "2",
        "--batch_size", "120",
        "--use_if"
    ]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    start_time = time.time()
    
    try:
        # 运行训练
        process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True,
            env=env
        )

        # 实时读取输出并检查重复
        output_lines = []
        duplicate_count = 0
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                output_lines.append(output)
                # 检查是否有重复的日志格式
                if "INFO -" in output and "[Group A" in output:
                    duplicate_count += 1
                    logger.warning(f"发现可能的重复日志: {output.strip()}")
                else:
                    logger.info(f"[Test] {output.strip()}")

        return_code = process.poll()
        end_time = time.time()
        duration = end_time - start_time
        
        if return_code == 0:
            logger.info(f"✅ 批量训练测试成功完成，耗时: {duration:.2f}秒")
            
            if duplicate_count == 0:
                logger.info("✅ 没有发现重复日志输出 - 修复成功！")
                return True
            else:
                logger.warning(f"⚠️ 发现 {duplicate_count} 条可能的重复日志")
                return False
        else:
            logger.error(f"❌ 批量训练测试失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 批量训练测试出现异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 快速测试批量训练日志修复效果")
    print("="*50)
    
    success = test_batch_logging()
    
    print("\n" + "="*50)
    if success:
        print("🎉 批量训练日志修复测试通过！")
        print("现在可以安全地运行完整的批量训练了。")
        print("\n建议运行:")
        print("python run_all_groups_with_if.py")
    else:
        print("⚠️ 批量训练日志仍有问题，需要进一步检查。")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
